#!/usr/bin/env python3
"""
Basic test for auto_samurai_pipeline functionality
"""

import sys
import os
from auto_samurai_pipeline import AutoSamuraiPipeline

def test_basic_functionality():
    """Test basic functionality without SAMURAI initialization"""
    
    print("🧪 Testing basic auto_samurai_pipeline functionality")
    print("=" * 60)
    
    # Test data loading
    pipeline = AutoSamuraiPipeline()
    
    # Test with existing output directory
    test_dir = "output_held_detection_amino_energy_grape_frame45"
    
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return False
    
    print(f"📂 Testing data loading from: {test_dir}")
    
    # Load auto_labelme results
    labelme_data = pipeline.load_auto_labelme_results(test_dir)
    
    if not labelme_data:
        print("❌ Failed to load labelme data")
        return False
    
    print("✅ Successfully loaded labelme data:")
    print(f"   Video: {labelme_data['video_name']}")
    print(f"   Seed frame: {labelme_data['seed_frame']}")
    print(f"   Label: {labelme_data['label']}")
    print(f"   Bbox: {labelme_data['bbox']}")
    
    # Test video trimming
    print(f"\n🎬 Testing video trimming...")
    
    video_path = labelme_data['video_path']
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        return False
    
    # Create test output directory
    test_output_dir = "test_samurai_output"
    os.makedirs(test_output_dir, exist_ok=True)
    
    trimmed_video_path = os.path.join(test_output_dir, "test_trimmed.mp4")
    
    success = pipeline.create_trimmed_video(
        video_path=video_path,
        start_frame=labelme_data['seed_frame'],
        output_path=trimmed_video_path
    )
    
    if success:
        print(f"✅ Successfully created trimmed video: {trimmed_video_path}")
        
        # Check if file exists and has reasonable size
        if os.path.exists(trimmed_video_path):
            file_size = os.path.getsize(trimmed_video_path)
            print(f"   File size: {file_size / (1024*1024):.1f} MB")
        
        # Clean up
        if os.path.exists(trimmed_video_path):
            os.remove(trimmed_video_path)
    else:
        print("❌ Failed to create trimmed video")
        return False
    
    # Test re-detection functionality
    print(f"\n🔍 Testing re-detection functionality...")
    
    # This would normally call auto_labelme_yolo, but let's just test the parsing logic
    print("   (Skipping actual re-detection call for basic test)")
    
    # Clean up test directory
    if os.path.exists(test_output_dir):
        os.rmdir(test_output_dir)
    
    print("\n🎉 All basic tests passed!")
    return True

def main():
    """Main test function"""
    success = test_basic_functionality()
    
    if success:
        print("\n✅ Basic functionality test completed successfully")
        print("\nTo run the full pipeline with SAMURAI:")
        print("  python auto_samurai_pipeline.py output_held_detection_amino_energy_grape_frame45")
    else:
        print("\n❌ Basic functionality test failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
