import cv2
import json
import os
import glob
from PIL import Image
from ultralytics import YOLO
import mediapipe as mp

# --- CONFIG ---
VIDEO_NAME = "amino_energy_grape"
INPUT_DIR = f"processed_new_test_videos/{VIDEO_NAME}"
OUTPUT_DIR = f"output_held_detection_{VIDEO_NAME}"
YOLO_MODEL_PATH = "yolov8s.pt"
TARGET_CLASSES = ['bottle', 'can']
START_FRAME = 30  # Start from frame 30

os.makedirs(OUTPUT_DIR, exist_ok=True)

# Load YOLOv8
yolo = YOLO(YOLO_MODEL_PATH)

# Load MediaPipe Hands
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=True, max_num_hands=2)
mp_drawing = mp.solutions.drawing_utils

# Get all frame files starting from frame 30
frame_pattern = os.path.join(INPUT_DIR, f"{VIDEO_NAME}_frame_*.png")
all_frames = sorted(glob.glob(frame_pattern))

# Filter frames starting from frame 30
frames_to_process = []
for frame_path in all_frames:
    frame_num = int(os.path.basename(frame_path).split('_frame_')[1].split('.')[0])
    if frame_num >= START_FRAME:
        frames_to_process.append(frame_path)

print(f"Found {len(frames_to_process)} frames to process starting from frame {START_FRAME}")

def process_frame(frame_path):
    """Process a single frame and return detection results"""
    frame = cv2.imread(frame_path)
    if frame is None:
        print(f"Could not load frame: {frame_path}")
        return None

    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    h, w, _ = frame.shape
    frame_name = os.path.basename(frame_path)

    # Run object detection
    results = yolo(frame)[0]

    # Filter relevant objects
    product_boxes = []
    for box in results.boxes:
        cls_id = int(box.cls[0])
        label = results.names[cls_id]
        if label in TARGET_CLASSES:
            x1, y1, x2, y2 = map(float, box.xyxy[0])
            product_boxes.append((label, (x1, y1, x2, y2)))

    # Detect hands
    hand_results = hands.process(rgb)
    hand_centers = []

    if hand_results.multi_hand_landmarks:
        for landmarks in hand_results.multi_hand_landmarks:
            x = int(landmarks.landmark[mp_hands.HandLandmark.WRIST].x * w)
            y = int(landmarks.landmark[mp_hands.HandLandmark.WRIST].y * h)
            hand_centers.append((x, y))

    # Find product box closest to any hand
    def box_center(box):
        x1, y1, x2, y2 = box
        return ((x1 + x2) / 2, (y1 + y2) / 2)

    closest_box = None
    min_dist = float('inf')

    for label, box in product_boxes:
        bx, by = box_center(box)
        for hx, hy in hand_centers:
            dist = ((bx - hx) ** 2 + (by - hy) ** 2) ** 0.5
            if dist < min_dist:
                min_dist = dist
                closest_box = (label, box)

    # Draw results
    if closest_box:
        label, (x1, y1, x2, y2) = closest_box
        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 3)
        cv2.putText(frame, label, (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)

    # Save image
    frame_base = os.path.splitext(frame_name)[0]
    out_path = os.path.join(OUTPUT_DIR, f"{frame_base}_detected.jpg")
    cv2.imwrite(out_path, frame)

    # Generate LabelMe-style JSON
    if closest_box:
        label, (x1, y1, x2, y2) = closest_box
        labelme_json = {
            "version": "5.0.1",
            "flags": {},
            "shapes": [
                {
                    "label": label,
                    "points": [[x1, y1], [x2, y2]],
                    "group_id": None,
                    "shape_type": "rectangle",
                    "flags": {}
                }
            ],
            "imagePath": frame_name,
            "imageHeight": h,
            "imageWidth": w
        }

        json_path = os.path.join(OUTPUT_DIR, f"{frame_base}.json")
        with open(json_path, 'w') as f:
            json.dump(labelme_json, f, indent=4)

        return {
            'frame_path': frame_path,
            'detection': closest_box,
            'output_image': out_path,
            'output_json': json_path
        }
    else:
        return {
            'frame_path': frame_path,
            'detection': None,
            'output_image': out_path,
            'output_json': None
        }

# Process all frames starting from frame 30
results = []
processed_count = 0
detected_count = 0

print(f"Processing {len(frames_to_process)} frames...")
for i, frame_path in enumerate(frames_to_process):
    print(f"Processing frame {i+1}/{len(frames_to_process)}: {os.path.basename(frame_path)}")

    result = process_frame(frame_path)
    if result:
        results.append(result)
        processed_count += 1
        if result['detection']:
            detected_count += 1
            print(f"  ✅ Detected: {result['detection'][0]}")
        else:
            print(f"  ❌ No held object found")

print(f"\n🎉 Processing complete!")
print(f"📊 Summary:")
print(f"  - Total frames processed: {processed_count}")
print(f"  - Frames with detections: {detected_count}")
print(f"  - Detection rate: {detected_count/processed_count*100:.1f}%" if processed_count > 0 else "  - Detection rate: 0%")
print(f"📁 Output directory: {OUTPUT_DIR}")

# Save summary report
summary_path = os.path.join(OUTPUT_DIR, "detection_summary.json")
summary_data = {
    "video_name": VIDEO_NAME,
    "start_frame": START_FRAME,
    "total_frames_processed": processed_count,
    "frames_with_detections": detected_count,
    "detection_rate": detected_count/processed_count if processed_count > 0 else 0,
    "results": results
}

with open(summary_path, 'w') as f:
    json.dump(summary_data, f, indent=4)

print(f"📄 Summary report saved to: {summary_path}")