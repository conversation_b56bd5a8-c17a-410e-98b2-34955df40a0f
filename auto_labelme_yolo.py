import cv2
import json
import os
from PIL import Image
from ultralytics import YOLO
import mediapipe as mp

# --- CONFIG ---
INPUT_IMAGE = "frame0.jpg"
OUTPUT_DIR = "output_held_detection"
YOLO_MODEL_PATH = "yolov8s.pt"
TARGET_CLASSES = ['bottle', 'can']

os.makedirs(OUTPUT_DIR, exist_ok=True)

# Load YOLOv8
yolo = YOLO(YOLO_MODEL_PATH)

# Load MediaPipe Hands
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=True, max_num_hands=2)
mp_drawing = mp.solutions.drawing_utils

# Load image
frame = cv2.imread(INPUT_IMAGE)
rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
h, w, _ = frame.shape

# Run object detection
results = yolo(frame)[0]

# Filter relevant objects
product_boxes = []
for box in results.boxes:
    cls_id = int(box.cls[0])
    label = results.names[cls_id]
    if label in TARGET_CLASSES:
        x1, y1, x2, y2 = map(float, box.xyxy[0])
        product_boxes.append((label, (x1, y1, x2, y2)))

# Detect hands
hand_results = hands.process(rgb)
hand_centers = []

if hand_results.multi_hand_landmarks:
    for landmarks in hand_results.multi_hand_landmarks:
        x = int(landmarks.landmark[mp_hands.HandLandmark.WRIST].x * w)
        y = int(landmarks.landmark[mp_hands.HandLandmark.WRIST].y * h)
        hand_centers.append((x, y))

# Find product box closest to any hand
def box_center(box):
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

closest_box = None
min_dist = float('inf')

for label, box in product_boxes:
    bx, by = box_center(box)
    for hx, hy in hand_centers:
        dist = ((bx - hx) ** 2 + (by - hy) ** 2) ** 0.5
        if dist < min_dist:
            min_dist = dist
            closest_box = (label, box)

# Draw results
if closest_box:
    label, (x1, y1, x2, y2) = closest_box
    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 3)
    cv2.putText(frame, label, (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)

# Save image
out_path = os.path.join(OUTPUT_DIR, "held_object_detected.jpg")
cv2.imwrite(out_path, frame)
print(f"Held object bounding box saved to: {out_path}")

# Generate LabelMe-style JSON
if closest_box:
    label, (x1, y1, x2, y2) = closest_box
    labelme_json = {
        "version": "5.0.1",
        "flags": {},
        "shapes": [
            {
                "label": label,
                "points": [[x1, y1], [x2, y2]],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            }
        ],
        "imagePath": os.path.basename(INPUT_IMAGE),
        "imageHeight": h,
        "imageWidth": w
    }

    json_path = os.path.join(OUTPUT_DIR, "frame0.json")
    with open(json_path, 'w') as f:
        json.dump(labelme_json, f, indent=4)

    print("Saved LabelMe JSON for held object.")
else:
    print("No held object found.")