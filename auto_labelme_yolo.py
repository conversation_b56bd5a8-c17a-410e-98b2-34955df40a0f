import cv2
import json
import os
from PIL import Image
from ultralytics import YOLO
import mediapipe as mp

# --- CONFIG ---
VIDEO_PATH = "new_test_videos/amino_energy_grape.mp4"
FRAME_NUMBER = 45
OUTPUT_DIR = "output_held_detection_amino_grape_frame45"
YOLO_MODEL_PATH = "yolov8s.pt"
# Let's be more inclusive and check all small objects that could be held
TARGET_CLASSES = ['bottle', 'can', 'cup', 'wine glass', 'cell phone', 'remote', 'mouse', 'keyboard', 'book', 'scissors', 'teddy bear', 'hair drier', 'toothbrush', 'banana', 'apple', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake']

os.makedirs(OUTPUT_DIR, exist_ok=True)

# Load YOLOv8
yolo = YOLO(YOLO_MODEL_PATH)

# Load MediaPipe Hands
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=True, max_num_hands=2)
mp_drawing = mp.solutions.drawing_utils

# Extract specific frame from video
cap = cv2.VideoCapture(VIDEO_PATH)
cap.set(cv2.CAP_PROP_POS_FRAMES, FRAME_NUMBER)
ret, frame = cap.read()
cap.release()

if not ret:
    print(f"Could not read frame {FRAME_NUMBER} from {VIDEO_PATH}")
    exit(1)

# Save the extracted frame for reference
frame_path = os.path.join(OUTPUT_DIR, f"amino_grape_frame_{FRAME_NUMBER}.jpg")
cv2.imwrite(frame_path, frame)
print(f"Extracted frame {FRAME_NUMBER} saved to: {frame_path}")

rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
h, w, _ = frame.shape

# Run object detection with very low confidence threshold to catch more objects
results = yolo(frame, conf=0.05)[0]  # Very low confidence threshold to catch more objects

# Print all detected objects for debugging
print("All detected objects:")
for box in results.boxes:
    cls_id = int(box.cls[0])
    label = results.names[cls_id]
    confidence = float(box.conf[0])
    x1, y1, x2, y2 = map(float, box.xyxy[0])
    print(f"  {label}: {confidence:.3f} at ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")

# Filter relevant objects
product_boxes = []
for box in results.boxes:
    cls_id = int(box.cls[0])
    label = results.names[cls_id]
    if label in TARGET_CLASSES:
        x1, y1, x2, y2 = map(float, box.xyxy[0])
        product_boxes.append((label, (x1, y1, x2, y2)))

print(f"Found {len(product_boxes)} product boxes: {[label for label, _ in product_boxes]}")

# Detect hands
hand_results = hands.process(rgb)
hand_centers = []

if hand_results.multi_hand_landmarks:
    for landmarks in hand_results.multi_hand_landmarks:
        x = int(landmarks.landmark[mp_hands.HandLandmark.WRIST].x * w)
        y = int(landmarks.landmark[mp_hands.HandLandmark.WRIST].y * h)
        hand_centers.append((x, y))
        print(f"Hand detected at wrist position: ({x}, {y})")
else:
    print("No hands detected")

print(f"Found {len(hand_centers)} hands")

# Find product box closest to any hand, or just the first product if no hands detected
def box_center(box):
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

closest_box = None
min_dist = float('inf')

if hand_centers and product_boxes:
    # Find product closest to hand
    for label, box in product_boxes:
        bx, by = box_center(box)
        for hx, hy in hand_centers:
            dist = ((bx - hx) ** 2 + (by - hy) ** 2) ** 0.5
            if dist < min_dist:
                min_dist = dist
                closest_box = (label, box)
elif product_boxes:
    # No hands detected, choose the product box most likely to be held (prefer bottom middle area)
    def score_box_for_holding(label, box):
        x1, y1, x2, y2 = box
        center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
        box_width, box_height = x2 - x1, y2 - y1

        # Strongly prefer objects in the lower portion of the frame (where hands hold items)
        # Bottom third gets highest score, middle third gets medium, top third gets low score
        if center_y > h * 0.66:  # Bottom third
            vertical_score = 1.0
        elif center_y > h * 0.33:  # Middle third
            vertical_score = 0.7
        else:  # Top third (likely stationary items in fridge)
            vertical_score = 0.2

        # Prefer objects closer to horizontal center (slightly left as mentioned)
        target_x = w * 0.45  # Slightly left of center
        horizontal_distance = abs(center_x - target_x) / w
        horizontal_score = 1.0 / (1.0 + horizontal_distance * 2)

        # Prefer reasonable sizes for handheld items (not too large, not too small)
        size_ratio = (box_width * box_height) / (w * h)
        if 0.005 < size_ratio < 0.15:  # Good size for handheld item
            size_score = 1.0
        elif 0.001 < size_ratio < 0.3:  # Acceptable size
            size_score = 0.7
        else:  # Too large (likely background) or too small
            size_score = 0.3

        # Prefer bottles and cans over other objects
        type_score = 1.0 if label in ['bottle', 'can'] else 0.6

        total_score = vertical_score * horizontal_score * size_score * type_score

        print(f"  {label} at ({center_x:.0f}, {center_y:.0f}): vertical={vertical_score:.2f}, horizontal={horizontal_score:.2f}, size={size_score:.2f}, type={type_score:.2f} -> total={total_score:.3f}")

        return total_score

    # Score all product boxes and choose the best one
    print("Scoring product boxes for handheld likelihood:")
    scored_boxes = [(score_box_for_holding(label, box), label, box) for label, box in product_boxes]
    scored_boxes.sort(reverse=True)

    best_score, best_label, best_box = scored_boxes[0]
    closest_box = (best_label, best_box)
    print(f"Selected product: {best_label} (score: {best_score:.3f})")

# Draw results
if closest_box:
    label, (x1, y1, x2, y2) = closest_box
    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 3)
    cv2.putText(frame, label, (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)

# Save image with detection
out_path = os.path.join(OUTPUT_DIR, f"amino_grape_frame_{FRAME_NUMBER}_detected.jpg")
cv2.imwrite(out_path, frame)
print(f"Held object bounding box saved to: {out_path}")

# Generate LabelMe-style JSON
if closest_box:
    label, (x1, y1, x2, y2) = closest_box
    labelme_json = {
        "version": "5.0.1",
        "flags": {},
        "shapes": [
            {
                "label": label,
                "points": [[x1, y1], [x2, y2]],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {}
            }
        ],
        "imagePath": f"amino_grape_frame_{FRAME_NUMBER}.jpg",
        "imageHeight": h,
        "imageWidth": w
    }

    json_path = os.path.join(OUTPUT_DIR, f"amino_grape_frame_{FRAME_NUMBER}.json")
    with open(json_path, 'w') as f:
        json.dump(labelme_json, f, indent=4)

    print(f"Saved LabelMe JSON for held object: {json_path}")
    print(f"Detection: {label} at coordinates ({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
else:
    print("No held object found.")