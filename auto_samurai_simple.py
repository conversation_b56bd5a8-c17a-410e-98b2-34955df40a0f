#!/usr/bin/env python3
"""
Simplified Auto SAMURAI Pipeline
================================

This script runs SAMURAI tracking based on auto_labelme_yolo detection results
by leveraging existing working SAMURAI scripts as subprocess calls.

Usage:
    python auto_samurai_simple.py <auto_labelme_output_dir>
"""

import os
import sys
import json
import cv2
import subprocess
import tempfile
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleAutoSamuraiPipeline:
    """Simplified pipeline using existing SAMURAI scripts"""
    
    def __init__(self):
        pass
    
    def load_auto_labelme_results(self, output_dir: str):
        """Load results from auto_labelme_yolo output directory"""
        output_path = Path(output_dir)
        
        if not output_path.exists():
            logger.error(f"Output directory not found: {output_dir}")
            return None
        
        # Find JSON file
        json_files = list(output_path.glob("*.json"))
        if not json_files:
            logger.error(f"No JSON annotation file found in {output_dir}")
            return None
        
        json_file = json_files[0]
        
        # Extract video info from directory name
        dir_name = output_path.name
        parts = dir_name.split('_')
        if len(parts) < 5:
            logger.error(f"Cannot parse video name from directory: {dir_name}")
            return None
        
        video_name = '_'.join(parts[3:-1])
        frame_number = int(parts[-1].replace('frame', ''))
        video_path = f"new_test_videos/{video_name}.mp4"
        
        if not os.path.exists(video_path):
            logger.error(f"Video file not found: {video_path}")
            return None
        
        # Load JSON annotation
        try:
            with open(json_file, 'r') as f:
                annotation = json.load(f)
            
            shape = annotation['shapes'][0]
            points = shape['points']
            label = shape['label']
            
            # Convert to bounding box [x1, y1, x2, y2]
            x1, y1 = points[0]
            x2, y2 = points[1]
            bbox = [float(x1), float(y1), float(x2), float(y2)]
            
            return {
                'video_path': video_path,
                'video_name': video_name,
                'seed_frame': frame_number,
                'bbox': bbox,
                'label': label,
                'json_file': str(json_file),
                'output_dir': str(output_path)
            }
            
        except Exception as e:
            logger.error(f"Failed to load annotation: {e}")
            return None
    
    def create_temp_annotation_file(self, labelme_data, temp_dir):
        """Create temporary annotation file in the format expected by existing SAMURAI scripts"""
        
        # Create a temporary processed video structure
        video_name = labelme_data['video_name']
        temp_video_dir = Path(temp_dir) / video_name
        temp_video_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy video to temp location
        temp_video_path = temp_video_dir / f"{video_name}.mp4"
        subprocess.run(['cp', labelme_data['video_path'], str(temp_video_path)], check=True)
        
        # Create annotation in the format expected by existing scripts
        annotation_data = {
            'frame_number': labelme_data['seed_frame'],
            'bounding_boxes': [labelme_data['bbox']],
            'labels': [labelme_data['label']]
        }
        
        annotation_file = temp_video_dir / f"frame_{labelme_data['seed_frame']:04d}.json"
        with open(annotation_file, 'w') as f:
            json.dump(annotation_data, f, indent=2)
        
        return str(temp_video_dir), str(annotation_file)
    
    def run_samurai_with_existing_script(self, labelme_data, output_dir):
        """Run SAMURAI using the existing new_test_videos_samurai_tracking.py script"""
        
        try:
            # Create temporary directory structure
            with tempfile.TemporaryDirectory() as temp_dir:
                logger.info(f"🔧 Setting up temporary structure in {temp_dir}")
                
                temp_video_dir, annotation_file = self.create_temp_annotation_file(labelme_data, temp_dir)
                
                # Create a simple script to run SAMURAI on our specific video
                script_content = f'''
import sys
sys.path.append('.')
from new_test_videos_samurai_tracking import NewTestVideoSamuraiTracker
import logging

logging.basicConfig(level=logging.INFO)

# Create tracker
tracker = NewTestVideoSamuraiTracker(
    processed_videos_dir="{temp_dir}",
    new_test_videos_dir="{temp_dir}"
)

# Initialize SAMURAI
if not tracker.initialize_samurai():
    print("Failed to initialize SAMURAI")
    sys.exit(1)

# Process the single video
video_name = "{labelme_data['video_name']}"
results = tracker.process_single_video(video_name)

print("SAMURAI tracking completed")
print(f"Results: {{results}}")
'''
                
                script_path = Path(temp_dir) / "run_samurai.py"
                with open(script_path, 'w') as f:
                    f.write(script_content)
                
                # Run the script
                logger.info(f"🚀 Running SAMURAI tracking...")
                result = subprocess.run(
                    ['python', str(script_path)],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )
                
                if result.returncode == 0:
                    logger.info("✅ SAMURAI tracking completed successfully")
                    
                    # Copy results to output directory
                    samurai_results_dir = Path(temp_dir) / "new_test_videos_samurai_results" / labelme_data['video_name']
                    if samurai_results_dir.exists():
                        output_samurai_dir = Path(output_dir) / "samurai_tracking"
                        subprocess.run(['cp', '-r', str(samurai_results_dir), str(output_samurai_dir)], check=True)
                        logger.info(f"📁 Results copied to {output_samurai_dir}")
                    
                    return {'status': 'success', 'output': result.stdout}
                else:
                    logger.error(f"❌ SAMURAI tracking failed: {result.stderr}")
                    return {'status': 'failed', 'error': result.stderr}
                    
        except Exception as e:
            logger.error(f"❌ Error running SAMURAI: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def process_auto_labelme_output(self, output_dir: str):
        """Main pipeline function"""
        
        # Load auto_labelme results
        labelme_data = self.load_auto_labelme_results(output_dir)
        if not labelme_data:
            return {'status': 'failed', 'error': 'failed_to_load_labelme_data'}
        
        logger.info(f"🎯 Processing {labelme_data['video_name']} from frame {labelme_data['seed_frame']}")
        
        # Run SAMURAI tracking
        result = self.run_samurai_with_existing_script(labelme_data, output_dir)
        
        return result

def main():
    """Main function"""
    if len(sys.argv) < 2 or sys.argv[1] in ['-h', '--help']:
        print("Usage: python auto_samurai_simple.py <auto_labelme_output_dir>")
        print("")
        print("Examples:")
        print("  python auto_samurai_simple.py output_held_detection_amino_energy_grape_frame60")
        print("")
        print("This simplified script uses existing SAMURAI scripts as subprocess calls")
        sys.exit(0)
    
    output_dir = sys.argv[1]
    
    if not os.path.exists(output_dir):
        logger.error(f"Output directory not found: {output_dir}")
        sys.exit(1)
    
    # Create pipeline
    pipeline = SimpleAutoSamuraiPipeline()
    
    # Process the auto_labelme output
    logger.info(f"🚀 Processing auto_labelme output: {output_dir}")
    result = pipeline.process_auto_labelme_output(output_dir)
    
    if result['status'] == 'success':
        logger.info("🎉 Pipeline completed successfully!")
    else:
        logger.error(f"❌ Pipeline failed: {result.get('error', 'unknown error')}")
        sys.exit(1)

if __name__ == "__main__":
    main()
