#!/usr/bin/env python3
"""
Demo Auto SAMURAI Pipeline
==========================

This script demonstrates the auto SAMURAI pipeline concept by:
1. Loading auto_labelme_yolo detection results
2. Creating the proper directory structure for SAMURAI tracking
3. Simulating the tracking process and re-detection workflow
4. Showing how the pipeline would handle tracking failures and re-detection

Usage:
    python demo_auto_samurai_pipeline.py <auto_labelme_output_dir>
"""

import os
import sys
import json
import cv2
import numpy as np
from pathlib import Path
import logging
import subprocess

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DemoAutoSamuraiPipeline:
    """Demo pipeline showing the auto SAMURAI concept"""
    
    def __init__(self):
        pass
    
    def load_auto_labelme_results(self, output_dir: str):
        """Load results from auto_labelme_yolo output directory"""
        output_path = Path(output_dir)
        
        if not output_path.exists():
            logger.error(f"Output directory not found: {output_dir}")
            return None
        
        # Find JSON file
        json_files = list(output_path.glob("*.json"))
        if not json_files:
            logger.error(f"No JSON annotation file found in {output_dir}")
            return None
        
        json_file = json_files[0]
        
        # Extract video info from directory name
        dir_name = output_path.name
        parts = dir_name.split('_')
        if len(parts) < 5:
            logger.error(f"Cannot parse video name from directory: {dir_name}")
            return None
        
        video_name = '_'.join(parts[3:-1])
        frame_number = int(parts[-1].replace('frame', ''))
        video_path = f"new_test_videos/{video_name}.mp4"
        
        if not os.path.exists(video_path):
            logger.error(f"Video file not found: {video_path}")
            return None
        
        # Load JSON annotation
        try:
            with open(json_file, 'r') as f:
                annotation = json.load(f)
            
            shape = annotation['shapes'][0]
            points = shape['points']
            label = shape['label']
            
            # Convert to bounding box [x1, y1, x2, y2]
            x1, y1 = points[0]
            x2, y2 = points[1]
            bbox = [float(x1), float(y1), float(x2), float(y2)]
            
            return {
                'video_path': video_path,
                'video_name': video_name,
                'seed_frame': frame_number,
                'bbox': bbox,
                'label': label,
                'json_file': str(json_file),
                'output_dir': str(output_path)
            }
            
        except Exception as e:
            logger.error(f"Failed to load annotation: {e}")
            return None
    
    def simulate_samurai_tracking(self, labelme_data, output_dir, start_frame, bbox):
        """Simulate SAMURAI tracking process"""
        
        logger.info(f"🎯 Simulating SAMURAI tracking from frame {start_frame}")
        logger.info(f"📍 Initial bbox: {bbox}")
        
        # Get video info
        cap = cv2.VideoCapture(labelme_data['video_path'])
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        cap.release()
        
        logger.info(f"📊 Video: {total_frames} frames, {fps} FPS, {width}x{height}")
        
        # Simulate tracking for a portion of the video
        tracking_duration = min(int(fps * 10), total_frames - start_frame)  # Track for up to 10 seconds
        
        tracking_data = []
        current_bbox = bbox.copy()
        
        # Simulate gradual bbox movement and potential loss
        for i in range(tracking_duration):
            frame_num = start_frame + i
            
            # Simulate bbox movement (random walk)
            if i > 0:
                dx = np.random.randint(-10, 11)
                dy = np.random.randint(-10, 11)
                current_bbox[0] += dx
                current_bbox[1] += dy
                current_bbox[2] += dx
                current_bbox[3] += dy
                
                # Keep bbox within frame bounds
                current_bbox[0] = max(0, min(current_bbox[0], width - 50))
                current_bbox[1] = max(0, min(current_bbox[1], height - 50))
                current_bbox[2] = max(50, min(current_bbox[2], width))
                current_bbox[3] = max(50, min(current_bbox[3], height))
            
            # Simulate tracking confidence decay
            confidence = max(0.3, 1.0 - (i * 0.02))
            
            # Simulate tracking loss after some time (randomly)
            if i > int(fps * 3) and np.random.random() < 0.1:  # 10% chance of loss after 3 seconds
                logger.warning(f"⚠️ Simulated tracking loss at frame {frame_num}")
                break
            
            tracking_data.append({
                'frame_number': frame_num,
                'bbox': current_bbox.copy(),
                'confidence': confidence,
                'tracking_active': True
            })
        
        # Save tracking results
        tracking_file = output_dir / "simulated_tracking_data.json"
        with open(tracking_file, 'w') as f:
            json.dump({
                'video_path': labelme_data['video_path'],
                'start_frame': start_frame,
                'tracking_results': tracking_data,
                'tracking_lost': len(tracking_data) < tracking_duration
            }, f, indent=2)
        
        logger.info(f"📊 Simulated tracking for {len(tracking_data)} frames")
        
        return {
            'frames_tracked': len(tracking_data),
            'last_frame': start_frame + len(tracking_data) - 1,
            'tracking_lost': len(tracking_data) < tracking_duration,
            'tracking_file': str(tracking_file)
        }
    
    def simulate_re_detection(self, video_path, start_frame):
        """Simulate re-detection using auto_labelme_yolo"""
        
        logger.info(f"🔍 Simulating re-detection from frame {start_frame}")
        
        # In the real implementation, this would call:
        # subprocess.run(['python', 'auto_labelme_yolo.py', video_path, str(start_frame)])
        
        # For demo, simulate finding a new detection
        simulated_bbox = [
            np.random.randint(100, 800),  # x1
            np.random.randint(200, 600),  # y1
            np.random.randint(900, 1200), # x2
            np.random.randint(700, 900)   # y2
        ]
        
        logger.info(f"✅ Simulated re-detection: bbox {simulated_bbox}")
        
        return {
            'success': True,
            'bbox': simulated_bbox,
            'frame': start_frame,
            'label': 'can'
        }
    
    def process_auto_labelme_output(self, output_dir: str):
        """Main pipeline demonstration"""
        
        # Load auto_labelme results
        labelme_data = self.load_auto_labelme_results(output_dir)
        if not labelme_data:
            return {'status': 'failed', 'error': 'failed_to_load_labelme_data'}
        
        # Create output directory for SAMURAI results
        samurai_output_dir = Path(output_dir) / "samurai_tracking_demo"
        samurai_output_dir.mkdir(exist_ok=True)
        
        logger.info(f"🎯 Demo processing: {labelme_data['video_name']}")
        logger.info(f"📍 Initial detection at frame {labelme_data['seed_frame']}")
        logger.info(f"🏷️ Label: {labelme_data['label']}")
        logger.info(f"📦 Bbox: {labelme_data['bbox']}")
        
        results = {
            'status': 'success',
            'video_name': labelme_data['video_name'],
            'initial_detection': labelme_data,
            'tracking_segments': []
        }
        
        current_frame = labelme_data['seed_frame']
        current_bbox = labelme_data['bbox']
        segment_count = 0
        
        # Simulate multiple tracking segments with re-detection
        while segment_count < 3:  # Demo with up to 3 segments
            segment_count += 1
            logger.info(f"\n🚀 Demo Segment {segment_count}: Starting from frame {current_frame}")
            
            # Create segment directory
            segment_dir = samurai_output_dir / f"segment_{segment_count:02d}"
            segment_dir.mkdir(exist_ok=True)
            
            # Simulate SAMURAI tracking
            tracking_result = self.simulate_samurai_tracking(
                labelme_data, segment_dir, current_frame, current_bbox
            )
            
            segment_info = {
                'segment_id': segment_count,
                'start_frame': current_frame,
                'bbox': current_bbox,
                'tracking_result': tracking_result
            }
            
            if not tracking_result['tracking_lost']:
                logger.info(f"✅ Segment {segment_count} completed successfully")
                segment_info['status'] = 'completed'
                results['tracking_segments'].append(segment_info)
                break
            
            # Simulate re-detection
            last_frame = tracking_result['last_frame']
            re_detect_frame = last_frame + 30  # Skip ahead 30 frames
            
            logger.info(f"⚠️ Tracking lost at frame {last_frame}, attempting re-detection at frame {re_detect_frame}")
            
            re_detection = self.simulate_re_detection(labelme_data['video_path'], re_detect_frame)
            
            if not re_detection['success']:
                logger.warning(f"❌ Re-detection failed, ending demo")
                segment_info['status'] = 'tracking_lost_no_redetection'
                results['tracking_segments'].append(segment_info)
                break
            
            segment_info['status'] = 'tracking_lost_redetected'
            segment_info['re_detection'] = re_detection
            results['tracking_segments'].append(segment_info)
            
            # Continue with re-detected product
            current_frame = re_detection['frame']
            current_bbox = re_detection['bbox']
            
            logger.info(f"🔄 Continuing demo from frame {current_frame} with re-detected product")
        
        # Save overall results
        results_file = samurai_output_dir / "demo_pipeline_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"\n🎉 Demo completed with {len(results['tracking_segments'])} segments")
        logger.info(f"📄 Demo results saved to: {results_file}")
        
        return results

def main():
    """Main function"""
    if len(sys.argv) < 2 or sys.argv[1] in ['-h', '--help']:
        print("Usage: python demo_auto_samurai_pipeline.py <auto_labelme_output_dir>")
        print("")
        print("Examples:")
        print("  python demo_auto_samurai_pipeline.py output_held_detection_amino_energy_grape_frame60")
        print("")
        print("This demo script shows how the auto SAMURAI pipeline would work:")
        print("  1. Load detection results from auto_labelme_yolo")
        print("  2. Simulate SAMURAI tracking from detection frame onwards")
        print("  3. Simulate tracking failures and re-detection")
        print("  4. Show multi-segment tracking workflow")
        sys.exit(0)
    
    output_dir = sys.argv[1]
    
    if not os.path.exists(output_dir):
        logger.error(f"Output directory not found: {output_dir}")
        sys.exit(1)
    
    # Create demo pipeline
    pipeline = DemoAutoSamuraiPipeline()
    
    # Process the auto_labelme output
    logger.info(f"🚀 Demo processing auto_labelme output: {output_dir}")
    result = pipeline.process_auto_labelme_output(output_dir)
    
    if result['status'] == 'success':
        logger.info("🎉 Demo pipeline completed successfully!")
        
        # Print summary
        segments = result['tracking_segments']
        logger.info(f"\n📊 DEMO SUMMARY:")
        logger.info(f"Video: {result['video_name']}")
        logger.info(f"Total segments: {len(segments)}")
        
        for i, segment in enumerate(segments, 1):
            status = segment['status']
            start_frame = segment['start_frame']
            frames_tracked = segment['tracking_result']['frames_tracked']
            logger.info(f"  Segment {i}: {status} (started at frame {start_frame}, tracked {frames_tracked} frames)")
    else:
        logger.error(f"❌ Demo pipeline failed: {result.get('error', 'unknown error')}")
        sys.exit(1)

if __name__ == "__main__":
    main()
