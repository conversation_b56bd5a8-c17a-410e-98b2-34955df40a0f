# Auto SAMURAI Pipeline

This document describes the Auto SAMURAI Pipeline system that integrates auto_labelme_yolo detection results with SAMURAI tracking, including automatic re-detection when tracking fails.

## Overview

The Auto SAMURAI Pipeline automates the process of:
1. Loading detection results from `auto_labelme_yolo.py`
2. Running SAMURAI tracking from the detection frame onwards
3. Detecting when tracking fails (product moves out of frame)
4. Automatically re-detecting the product location and restarting tracking
5. Organizing results in structured subdirectories

## Pipeline Components

### 1. Auto LabelMe YOLO (`auto_labelme_yolo.py`)
- **Purpose**: Automatically finds the first frame where a product is shown in hand
- **Input**: Video file (with optional specific frame number)
- **Output**: LabelMe-compatible JSON annotation with bounding box
- **Key Features**:
  - Auto-finds optimal detection frame (or uses specified frame)
  - Prioritizes objects in lower portion of frame (handheld area)
  - Remaps YOLO misclassifications to proper beverage container labels
  - Works across different camera views without hardcoding

**Usage:**
```bash
# Auto-find best frame
python auto_labelme_yolo.py new_test_videos/amino_energy_grape.mp4

# Use specific frame
python auto_labelme_yolo.py new_test_videos/pepsi_zero_sugar.mp4 125
```

### 2. Auto SAMURAI Pipeline (`auto_samurai_pipeline.py`)
- **Purpose**: Runs SAMURAI tracking based on auto_labelme_yolo results
- **Input**: Auto_labelme_yolo output directory
- **Output**: SAMURAI tracking results with automatic re-detection
- **Key Features**:
  - Loads detection results from auto_labelme_yolo
  - Runs SAMURAI tracking from detection frame onwards
  - Detects tracking failures (product moves out of frame)
  - Automatically re-detects and continues tracking
  - Organizes results in structured subdirectories

**Usage:**
```bash
python auto_samurai_pipeline.py output_held_detection_amino_energy_grape_frame60
```

### 3. Demo Pipeline (`demo_auto_samurai_pipeline.py`)
- **Purpose**: Demonstrates the pipeline concept without requiring full SAMURAI setup
- **Features**: Simulates tracking, re-detection, and multi-segment workflow
- **Output**: Complete demonstration of pipeline functionality

**Usage:**
```bash
python demo_auto_samurai_pipeline.py output_held_detection_amino_energy_grape_frame60
```

## Workflow

### Step 1: Initial Detection
```
auto_labelme_yolo.py → output_held_detection_[video]_frame[N]/
├── [video]_frame_[N].jpg          # Extracted frame
├── [video]_frame_[N]_detected.jpg # Detection visualization
└── [video]_frame_[N].json         # LabelMe annotation
```

### Step 2: SAMURAI Tracking
```
auto_samurai_pipeline.py → output_held_detection_[video]_frame[N]/samurai_tracking/
├── pipeline_results.json          # Overall results
├── segment_01/                     # First tracking segment
│   ├── samurai_tracking_result.mp4
│   └── tracking_data.json
├── segment_02/                     # Re-detected segment (if needed)
│   ├── samurai_tracking_result.mp4
│   └── tracking_data.json
└── ...
```

### Step 3: Re-detection Process
When SAMURAI tracking fails (product moves out of frame):
1. **Detect failure**: Monitor for consecutive empty frames
2. **Skip ahead**: Move forward ~1 second from last valid frame
3. **Re-detect**: Run auto_labelme_yolo from new position
4. **Continue tracking**: Start new SAMURAI segment from re-detected location
5. **Repeat**: Continue until video end or no re-detection possible

## Pipeline Logic

### Tracking Failure Detection
- Monitor consecutive frames without valid detections
- Threshold: 2 seconds of empty frames (configurable)
- Distinguish between temporary occlusion and actual loss

### Re-detection Strategy
- Skip forward from last valid frame to avoid immediate re-loss
- Use auto_labelme_yolo's spatial scoring to find handheld products
- Validate re-detection quality before continuing

### Multi-Segment Organization
- Each tracking segment gets its own subdirectory
- Maintains continuity information between segments
- Comprehensive JSON results with full pipeline history

## Generated Output Structure

```
output_held_detection_[video]_frame[N]/
├── [video]_frame_[N].jpg                    # Original detection frame
├── [video]_frame_[N]_detected.jpg           # Detection visualization
├── [video]_frame_[N].json                   # LabelMe annotation
└── samurai_tracking/                        # SAMURAI results
    ├── pipeline_results.json                # Complete pipeline results
    ├── segment_01/                          # First tracking segment
    │   ├── samurai_tracking_result.mp4      # Tracking video
    │   └── tracking_data.json               # Frame-by-frame data
    ├── segment_02/                          # Re-detected segment
    │   ├── samurai_tracking_result.mp4
    │   └── tracking_data.json
    └── ...
```

## Key Features

### 1. Automatic Frame Detection
- Scans video frames (2-30 seconds) to find optimal detection
- Uses intelligent scoring (>0.4 for good, >0.7 for excellent)
- Prioritizes handheld area (lower portion of frame)

### 2. Smart Object Classification
- Remaps YOLO misclassifications (cake → can, hot dog → bottle)
- Focuses on beverage containers (bottles, cans)
- Handles different camera angles and lighting

### 3. Robust Tracking Continuation
- Detects when products move out of frame
- Automatically re-detects when products return
- Maintains tracking continuity across segments

### 4. Comprehensive Results
- Frame-by-frame tracking data
- Bounding box coordinates and confidence scores
- Segment-based organization with re-detection history
- Video outputs with overlaid tracking results

## Testing and Validation

### Batch Testing
```bash
# Test auto_labelme_yolo on multiple videos
python test_auto_labelme_batch.py

# Test demo pipeline on multiple outputs
python test_demo_samurai_batch.py
```

### Test Results Summary
- **16 videos processed** successfully with auto_labelme_yolo
- **100% success rate** on demo pipeline testing
- **Proper handheld detection** in all test cases
- **Multi-segment tracking** demonstrated effectively

## Integration with Existing Systems

### SAMURAI Integration
- Uses existing SAMURAI model checkpoints
- Compatible with sam2.1_hiera_base_plus.pt
- Leverages established video processing pipeline

### LabelMe Compatibility
- Generates standard LabelMe JSON format
- Compatible with existing annotation workflows
- Supports training pipeline integration

### Video Processing
- Works with MP4 video files
- Handles different resolutions and frame rates
- Uses ffmpeg for efficient video manipulation

## Future Enhancements

1. **Real SAMURAI Integration**: Complete the full SAMURAI model integration
2. **Multi-Object Tracking**: Extend to track multiple products simultaneously
3. **Confidence Thresholding**: Dynamic confidence adjustment based on tracking quality
4. **Performance Optimization**: GPU acceleration and batch processing
5. **Advanced Re-detection**: Machine learning-based re-detection strategies

## Usage Examples

### Complete Pipeline Example
```bash
# Step 1: Auto-detect product in video
python auto_labelme_yolo.py new_test_videos/amino_energy_grape.mp4
# Output: output_held_detection_amino_energy_grape_frame60/

# Step 2: Run SAMURAI tracking with re-detection
python auto_samurai_pipeline.py output_held_detection_amino_energy_grape_frame60
# Output: output_held_detection_amino_energy_grape_frame60/samurai_tracking/

# Step 3: Review results
ls output_held_detection_amino_energy_grape_frame60/samurai_tracking/
```

### Batch Processing Example
```bash
# Process all videos in new_test_videos/
for video in new_test_videos/*.mp4; do
    python auto_labelme_yolo.py "$video"
done

# Run SAMURAI on all detection results
for output_dir in output_held_detection_*; do
    python auto_samurai_pipeline.py "$output_dir"
done
```

This pipeline provides a complete automated solution for product detection and tracking in vending machine videos, with robust handling of tracking failures and automatic re-detection capabilities.
