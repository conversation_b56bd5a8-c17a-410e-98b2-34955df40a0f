#!/usr/bin/env python3
"""
Batch Auto LabelMe for Canteen Fridge Videos
============================================

This script runs auto_labelme_yolo.py on all videos in the Canteen_Fridge_Videos directory
to automatically detect products in hand and generate bounding box annotations.

Since SAMURAI tracking is having initialization issues, this script focuses on just
getting the auto labelme detection results, which are working perfectly.

Usage:
    python batch_auto_labelme_canteen.py [--dry-run] [--product PRODUCT] [--max-runs N]
    
Options:
    --dry-run       Show what would be processed without actually running
    --product       Process only specific product (e.g., "Amino_Energy_Grape")
    --max-runs      Limit number of runs per product (default: all)
    --start-from    Start from specific product (alphabetically)
"""

import os
import sys
import subprocess
import glob
import json
import time
from pathlib import Path
import logging
import argparse
from typing import List, Dict, Tuple, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_auto_labelme_canteen.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CanteenAutoLabelMeProcessor:
    """Batch processor for Auto LabelMe on Canteen Fridge Videos"""
    
    def __init__(self, base_dir: str = "Canteen_Fridge_Videos/Canteen_Fridge_Videos"):
        self.base_dir = Path(base_dir)
        self.results_dir = Path("canteen_auto_labelme_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Processing statistics
        self.stats = {
            'total_videos': 0,
            'processed_videos': 0,
            'failed_videos': 0,
            'skipped_videos': 0,
            'auto_labelme_success': 0,
            'auto_labelme_failed': 0
        }
        
        # Track processing times
        self.processing_times = []
        
    def discover_videos(self, product_filter: Optional[str] = None, 
                       max_runs: Optional[int] = None) -> List[Dict]:
        """Discover all video files in the directory structure"""
        videos = []
        
        if not self.base_dir.exists():
            logger.error(f"Base directory not found: {self.base_dir}")
            return videos
        
        # Get all product directories
        product_dirs = [d for d in self.base_dir.iterdir() if d.is_dir()]
        product_dirs.sort()
        
        for product_dir in product_dirs:
            product_name = product_dir.name
            
            # Apply product filter
            if product_filter and product_name != product_filter:
                continue
            
            logger.info(f"📁 Scanning product: {product_name}")
            
            # Get all run directories
            run_dirs = [d for d in product_dir.iterdir() if d.is_dir() and d.name.startswith('run_')]
            run_dirs.sort(key=lambda x: int(x.name.split('_')[1]))
            
            # Apply max runs limit
            if max_runs:
                run_dirs = run_dirs[:max_runs]
            
            for run_dir in run_dirs:
                run_name = run_dir.name
                
                # Get all camera files
                cam_files = list(run_dir.glob('cam*.mp4'))
                cam_files.sort()
                
                for cam_file in cam_files:
                    cam_name = cam_file.stem  # cam0, cam1, etc.
                    
                    video_info = {
                        'product_name': product_name,
                        'run_name': run_name,
                        'cam_name': cam_name,
                        'video_path': str(cam_file),
                        'relative_path': str(cam_file.relative_to(self.base_dir)),
                        'output_prefix': f"{product_name}_{run_name}_{cam_name}"
                    }
                    videos.append(video_info)
        
        logger.info(f"📊 Discovered {len(videos)} videos across {len(set(v['product_name'] for v in videos))} products")
        return videos
    
    def check_existing_results(self, video_info: Dict) -> Dict[str, bool]:
        """Check if auto labelme results already exist for this video"""
        output_prefix = video_info['output_prefix']
        
        # Check for auto_labelme output - look for patterns that might match
        possible_patterns = [
            f"output_held_detection_{output_prefix}_frame*",
            f"output_held_detection_{video_info['cam_name']}_frame*",
            f"output_held_detection_{video_info['product_name'].lower()}_{video_info['run_name']}_{video_info['cam_name']}_frame*"
        ]
        
        auto_labelme_dirs = []
        for pattern in possible_patterns:
            auto_labelme_dirs.extend(glob.glob(pattern))
        
        return {
            'auto_labelme_exists': len(auto_labelme_dirs) > 0,
            'auto_labelme_dirs': auto_labelme_dirs
        }
    
    def run_auto_labelme(self, video_path: str, timeout: int = 300) -> Optional[str]:
        """Run auto_labelme_yolo.py on a video"""
        try:
            logger.info(f"🔍 Running auto labelme on: {video_path}")
            start_time = time.time()
            
            # Run auto_labelme_yolo.py
            result = subprocess.run(
                ['python', 'auto_labelme_yolo.py', video_path],
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            elapsed = time.time() - start_time
            
            if result.returncode == 0:
                logger.info(f"✅ Auto labelme completed in {elapsed:.1f}s")
                
                # Parse output to find the generated directory
                output_lines = result.stdout.split('\n')
                for line in output_lines:
                    if 'output_held_detection_' in line and 'frame' in line:
                        # Extract directory name
                        if 'Created output directory:' in line:
                            output_dir = line.split('Created output directory:')[-1].strip()
                            return output_dir
                        elif 'Final detection:' in line:
                            # Look for directory in nearby lines
                            continue
                
                # Fallback: look for recently created directories
                pattern = f"output_held_detection_*"
                recent_dirs = glob.glob(pattern)
                if recent_dirs:
                    # Return the most recently modified one
                    recent_dirs.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                    return recent_dirs[0]
                
                logger.warning("⚠️ Auto labelme succeeded but couldn't find output directory")
                return None
                
            else:
                logger.error(f"❌ Auto labelme failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ Auto labelme timed out after {timeout}s")
            return None
        except Exception as e:
            logger.error(f"💥 Auto labelme exception: {e}")
            return None
    
    def process_single_video(self, video_info: Dict, skip_existing: bool = True) -> Dict:
        """Process a single video with auto labelme"""
        video_path = video_info['video_path']
        output_prefix = video_info['output_prefix']
        
        logger.info(f"\n{'='*80}")
        logger.info(f"🎬 Processing: {video_info['product_name']} - {video_info['run_name']} - {video_info['cam_name']}")
        logger.info(f"📁 Video: {video_path}")
        logger.info(f"{'='*80}")
        
        result = {
            'video_info': video_info,
            'auto_labelme_success': False,
            'auto_labelme_output': None,
            'processing_time': 0,
            'status': 'started'
        }
        
        start_time = time.time()
        
        try:
            # Check existing results
            existing = self.check_existing_results(video_info)
            
            if skip_existing and existing['auto_labelme_exists']:
                logger.info(f"⏭️ Auto labelme results already exist, skipping")
                result['auto_labelme_success'] = True
                result['auto_labelme_output'] = existing['auto_labelme_dirs'][0]
                result['status'] = 'skipped'
                self.stats['skipped_videos'] += 1
            else:
                # Run auto labelme
                labelme_output = self.run_auto_labelme(video_path)
                
                if labelme_output:
                    result['auto_labelme_success'] = True
                    result['auto_labelme_output'] = labelme_output
                    result['status'] = 'completed'
                    self.stats['auto_labelme_success'] += 1
                    self.stats['processed_videos'] += 1
                else:
                    result['status'] = 'auto_labelme_failed'
                    self.stats['auto_labelme_failed'] += 1
                    self.stats['failed_videos'] += 1
        
        except Exception as e:
            logger.error(f"💥 Unexpected error processing video: {e}")
            result['status'] = 'exception'
            self.stats['failed_videos'] += 1
        
        finally:
            result['processing_time'] = time.time() - start_time
            self.processing_times.append(result['processing_time'])
        
        return result
    
    def save_processing_summary(self, results: List[Dict], output_file: str = None):
        """Save processing summary to JSON file"""
        if output_file is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_file = f"canteen_auto_labelme_summary_{timestamp}.json"
        
        summary = {
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'statistics': self.stats,
            'processing_times': {
                'total_time': sum(self.processing_times),
                'average_time': sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0,
                'min_time': min(self.processing_times) if self.processing_times else 0,
                'max_time': max(self.processing_times) if self.processing_times else 0
            },
            'results': results
        }
        
        with open(output_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"📄 Processing summary saved to: {output_file}")
        return output_file

    def print_final_summary(self, results: List[Dict]):
        """Print final processing summary"""
        logger.info(f"\n{'='*80}")
        logger.info(f"🎯 AUTO LABELME BATCH PROCESSING COMPLETED")
        logger.info(f"{'='*80}")

        # Overall statistics
        logger.info(f"📊 OVERALL STATISTICS:")
        logger.info(f"   Total videos discovered: {self.stats['total_videos']}")
        logger.info(f"   Successfully processed: {self.stats['processed_videos']}")
        logger.info(f"   Failed videos: {self.stats['failed_videos']}")
        logger.info(f"   Skipped videos: {self.stats['skipped_videos']}")

        # Auto labelme statistics
        logger.info(f"\n🔍 AUTO LABELME STATISTICS:")
        logger.info(f"   Successful: {self.stats['auto_labelme_success']}")
        logger.info(f"   Failed: {self.stats['auto_labelme_failed']}")
        success_rate = (self.stats['auto_labelme_success'] / max(self.stats['total_videos'], 1)) * 100
        logger.info(f"   Success Rate: {success_rate:.1f}%")

        # Timing statistics
        if self.processing_times:
            total_time = sum(self.processing_times)
            avg_time = total_time / len(self.processing_times)
            logger.info(f"\n⏱️ TIMING STATISTICS:")
            logger.info(f"   Total processing time: {total_time:.1f} seconds ({total_time/3600:.1f} hours)")
            logger.info(f"   Average time per video: {avg_time:.1f} seconds")
            logger.info(f"   Fastest video: {min(self.processing_times):.1f} seconds")
            logger.info(f"   Slowest video: {max(self.processing_times):.1f} seconds")

        # Product breakdown
        product_stats = {}
        for result in results:
            product = result['video_info']['product_name']
            if product not in product_stats:
                product_stats[product] = {'total': 0, 'success': 0, 'failed': 0}

            product_stats[product]['total'] += 1
            if result['status'] in ['completed', 'skipped']:
                product_stats[product]['success'] += 1
            else:
                product_stats[product]['failed'] += 1

        logger.info(f"\n📦 PRODUCT BREAKDOWN:")
        for product, stats in sorted(product_stats.items()):
            success_rate = (stats['success'] / stats['total']) * 100 if stats['total'] > 0 else 0
            logger.info(f"   {product}: {stats['success']}/{stats['total']} ({success_rate:.1f}% success)")

    def process_all_videos(self, product_filter: Optional[str] = None,
                          max_runs: Optional[int] = None,
                          dry_run: bool = False,
                          skip_existing: bool = True,
                          start_from: Optional[str] = None) -> List[Dict]:
        """Process all videos in the directory"""

        # Discover videos
        videos = self.discover_videos(product_filter, max_runs)
        self.stats['total_videos'] = len(videos)

        if len(videos) == 0:
            logger.warning("No videos found to process!")
            return []

        # Apply start_from filter
        if start_from:
            videos = [v for v in videos if v['product_name'] >= start_from]
            logger.info(f"📍 Starting from product: {start_from} ({len(videos)} videos)")

        if dry_run:
            logger.info(f"\n🔍 DRY RUN - Would process {len(videos)} videos:")
            for video in videos[:10]:  # Show first 10
                logger.info(f"   {video['product_name']} - {video['run_name']} - {video['cam_name']}")
            if len(videos) > 10:
                logger.info(f"   ... and {len(videos) - 10} more videos")
            return []

        # Process videos
        results = []

        logger.info(f"\n🚀 Starting auto labelme batch processing of {len(videos)} videos...")

        for i, video_info in enumerate(videos, 1):
            logger.info(f"\n📹 Processing video {i}/{len(videos)}")

            try:
                result = self.process_single_video(video_info, skip_existing)
                results.append(result)

                # Log progress every 10 videos
                if i % 10 == 0:
                    success_count = sum(1 for r in results if r['status'] in ['completed', 'skipped'])
                    logger.info(f"📊 Progress: {i}/{len(videos)} videos processed, {success_count} successful")

            except KeyboardInterrupt:
                logger.info(f"\n⚠️ Processing interrupted by user at video {i}/{len(videos)}")
                break
            except Exception as e:
                logger.error(f"💥 Unexpected error processing video {i}: {e}")
                continue

        # Save and print summary
        self.save_processing_summary(results)
        self.print_final_summary(results)

        return results


def main():
    """Main function with command-line interface"""
    parser = argparse.ArgumentParser(
        description="Batch process Canteen Fridge Videos with auto labelme detection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all videos
  python batch_auto_labelme_canteen.py

  # Dry run to see what would be processed
  python batch_auto_labelme_canteen.py --dry-run

  # Process only Amino Energy Grape videos
  python batch_auto_labelme_canteen.py --product Amino_Energy_Grape

  # Process first 2 runs of each product
  python batch_auto_labelme_canteen.py --max-runs 2

  # Start from a specific product (alphabetically)
  python batch_auto_labelme_canteen.py --start-from Pepsi_Diet

  # Force reprocessing (don't skip existing results)
  python batch_auto_labelme_canteen.py --no-skip-existing
        """
    )

    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be processed without actually running')
    parser.add_argument('--product', type=str,
                       help='Process only specific product (e.g., "Amino_Energy_Grape")')
    parser.add_argument('--max-runs', type=int,
                       help='Limit number of runs per product (default: all)')
    parser.add_argument('--start-from', type=str,
                       help='Start from specific product (alphabetically)')
    parser.add_argument('--no-skip-existing', action='store_true',
                       help='Force reprocessing (don\'t skip existing results)')

    args = parser.parse_args()

    # Create processor
    processor = CanteenAutoLabelMeProcessor()

    # Process videos
    results = processor.process_all_videos(
        product_filter=args.product,
        max_runs=args.max_runs,
        dry_run=args.dry_run,
        skip_existing=not args.no_skip_existing,
        start_from=args.start_from
    )

    if not args.dry_run and results:
        logger.info(f"\n✅ Auto labelme batch processing completed! Processed {len(results)} videos.")
        logger.info(f"📄 Check the processing summary JSON file for detailed results.")

        # Show some example results
        successful_results = [r for r in results if r['status'] in ['completed', 'skipped']]
        if successful_results:
            logger.info(f"\n📋 EXAMPLE SUCCESSFUL RESULTS:")
            for result in successful_results[:5]:  # Show first 5
                video_info = result['video_info']
                output_dir = result['auto_labelme_output']
                logger.info(f"   {video_info['product_name']} - {video_info['run_name']} - {video_info['cam_name']}: {output_dir}")


if __name__ == "__main__":
    main()
