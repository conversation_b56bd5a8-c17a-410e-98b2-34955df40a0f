#!/usr/bin/env python3
"""
Batch test script for demo_auto_samurai_pipeline.py
Tests the demo pipeline on multiple auto_labelme_yolo outputs
"""

import os
import subprocess
import glob
import json
from pathlib import Path

def test_demo_pipeline_on_output(output_dir):
    """Test the demo pipeline on a single output directory"""
    print(f"\n{'='*60}")
    print(f"Testing: {output_dir}")
    print(f"{'='*60}")
    
    if not os.path.exists(output_dir):
        print("❌ Directory not found")
        return {'status': 'directory_not_found'}
    
    try:
        # Run the demo pipeline
        result = subprocess.run(
            ['python', 'demo_auto_samurai_pipeline.py', output_dir],
            capture_output=True,
            text=True,
            timeout=60  # 1 minute timeout
        )
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            
            # Extract key information from output
            lines = result.stdout.split('\n')
            video_name = "unknown"
            segments_processed = 0
            
            for line in lines:
                if 'Demo processing:' in line:
                    video_name = line.split('Demo processing: ')[1].strip()
                elif 'Total segments:' in line:
                    segments_processed = int(line.split('Total segments: ')[1].strip())
            
            # Check if results were created
            demo_dir = Path(output_dir) / "samurai_tracking_demo"
            results_file = demo_dir / "demo_pipeline_results.json"
            
            if results_file.exists():
                with open(results_file, 'r') as f:
                    results_data = json.load(f)
                
                return {
                    'status': 'success',
                    'video_name': video_name,
                    'segments_processed': segments_processed,
                    'results_file': str(results_file),
                    'tracking_segments': len(results_data.get('tracking_segments', []))
                }
            else:
                return {
                    'status': 'success_no_results_file',
                    'video_name': video_name,
                    'segments_processed': segments_processed
                }
        else:
            print("❌ FAILED")
            print(f"   Error: {result.stderr}")
            return {
                'status': 'failed',
                'error': result.stderr,
                'stdout': result.stdout
            }
            
    except subprocess.TimeoutExpired:
        print("⏰ TIMEOUT - Demo took too long")
        return {'status': 'timeout'}
    except Exception as e:
        print(f"💥 EXCEPTION: {e}")
        return {'status': 'exception', 'error': str(e)}

def main():
    """Main batch testing function"""
    
    # Find all auto_labelme output directories
    output_pattern = "output_held_detection_*"
    output_dirs = glob.glob(output_pattern)
    
    if not output_dirs:
        print(f"No output directories found matching pattern: {output_pattern}")
        return
    
    print(f"Found {len(output_dirs)} auto_labelme output directories to test")
    
    # Test first 5 directories for demo
    test_dirs = sorted(output_dirs)[:5]
    
    print(f"Testing first {len(test_dirs)} directories:")
    for d in test_dirs:
        print(f"  - {d}")
    
    results = {}
    
    # Test each directory
    for output_dir in test_dirs:
        result = test_demo_pipeline_on_output(output_dir)
        results[output_dir] = result
    
    # Summary
    print(f"\n{'='*60}")
    print("BATCH DEMO TEST SUMMARY")
    print(f"{'='*60}")
    
    success_count = 0
    total_count = len(results)
    
    for output_dir, result in results.items():
        status = result['status']
        print(f"{output_dir}: {status}")
        
        if status == 'success':
            success_count += 1
            video_name = result.get('video_name', 'unknown')
            segments = result.get('segments_processed', 0)
            print(f"  Video: {video_name}, Segments: {segments}")
    
    print(f"\nSuccess rate: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    # Save detailed results
    results_file = "demo_samurai_batch_test_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"Detailed results saved to: {results_file}")
    
    # Show example of generated structure
    if success_count > 0:
        print(f"\n📁 Example generated structure:")
        for output_dir, result in results.items():
            if result['status'] == 'success':
                demo_dir = Path(output_dir) / "samurai_tracking_demo"
                if demo_dir.exists():
                    print(f"\n{output_dir}/")
                    print(f"  └── samurai_tracking_demo/")
                    print(f"      ├── demo_pipeline_results.json")
                    for segment_dir in sorted(demo_dir.glob("segment_*")):
                        print(f"      ├── {segment_dir.name}/")
                        print(f"      │   └── simulated_tracking_data.json")
                break

if __name__ == "__main__":
    main()
