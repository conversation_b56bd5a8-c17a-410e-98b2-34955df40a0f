#!/usr/bin/env python3
"""
Auto SAMURAI Pipeline
====================

This script runs SAMURAI tracking based on auto_labelme_yolo detection results.
It handles cases where tracking fails (product moves out of frame) by automatically
re-detecting and restarting tracking from the new location.

Usage:
    python auto_samurai_pipeline.py <auto_labelme_output_dir>
    python auto_samurai_pipeline.py output_held_detection_amino_energy_grape_frame60

Features:
- Runs SAMURAI tracking from auto_labelme_yolo detection frame onwards
- Detects when tracking fails (product moves out of frame)
- Automatically re-detects product location and restarts tracking
- Saves results in organized subdirectories
"""

import os
import sys
import json
import cv2
import torch
import numpy as np
from pathlib import Path
import logging
from typing import Optional, Tuple, List, Dict, Any
import subprocess
import tempfile

# Add SAMURAI to path
sys.path.append('samurai')
sys.path.append('samurai/sam2')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoSamuraiPipeline:
    """Pipeline that runs SAMURAI tracking from auto_labelme_yolo results"""
    
    def __init__(self, 
                 model_path: str = "samurai/sam2/checkpoints/sam2.1_hiera_base_plus.pt",
                 device: str = "cuda" if torch.cuda.is_available() else "cpu"):
        
        self.model_path = model_path
        self.device = device
        self.samurai_predictor = None
        
    def initialize_samurai(self) -> bool:
        """Initialize SAMURAI predictor"""
        try:
            # Clear any existing Hydra instance
            from hydra.core.global_hydra import GlobalHydra
            from hydra import initialize
            from sam2.build_sam import build_sam2_video_predictor
            
            if GlobalHydra.instance().is_initialized():
                GlobalHydra.instance().clear()
            
            # Initialize Hydra with SAM2.1 config directory
            config_dir = "samurai/sam2/sam2/configs/sam2.1"

            with initialize(config_path=config_dir, version_base=None):
                self.samurai_predictor = build_sam2_video_predictor(
                    config_file="sam2.1_hiera_b+.yaml",
                    ckpt_path=self.model_path,
                    device=self.device
                )
            
            logger.info("✅ SAMURAI model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize SAMURAI: {e}")
            return False
    
    def load_auto_labelme_results(self, output_dir: str) -> Optional[Dict[str, Any]]:
        """Load results from auto_labelme_yolo output directory"""
        output_path = Path(output_dir)
        
        if not output_path.exists():
            logger.error(f"Output directory not found: {output_dir}")
            return None
        
        # Find JSON file
        json_files = list(output_path.glob("*.json"))
        if not json_files:
            logger.error(f"No JSON annotation file found in {output_dir}")
            return None
        
        json_file = json_files[0]
        
        # Find original video path from directory name
        dir_name = output_path.name
        # Extract video name from directory name like "output_held_detection_amino_energy_grape_frame60"
        parts = dir_name.split('_')
        if len(parts) < 5:
            logger.error(f"Cannot parse video name from directory: {dir_name}")
            return None
        
        # Reconstruct video name and frame number
        video_name = '_'.join(parts[3:-1])  # Skip "output_held_detection" and "frameXX"
        frame_str = parts[-1]  # "frameXX"
        frame_number = int(frame_str.replace('frame', ''))
        
        video_path = f"new_test_videos/{video_name}.mp4"
        
        if not os.path.exists(video_path):
            logger.error(f"Video file not found: {video_path}")
            return None
        
        # Load JSON annotation
        try:
            with open(json_file, 'r') as f:
                annotation = json.load(f)
            
            # Extract bounding box from LabelMe format
            if not annotation.get('shapes'):
                logger.error("No shapes found in annotation")
                return None
            
            shape = annotation['shapes'][0]
            points = shape['points']
            label = shape['label']
            
            # Convert LabelMe points to bounding box [x1, y1, x2, y2]
            x1, y1 = points[0]
            x2, y2 = points[1]
            bbox = [float(x1), float(y1), float(x2), float(y2)]
            
            return {
                'video_path': video_path,
                'video_name': video_name,
                'seed_frame': frame_number,
                'bbox': bbox,
                'label': label,
                'json_file': str(json_file),
                'output_dir': str(output_path)
            }
            
        except Exception as e:
            logger.error(f"Failed to load annotation: {e}")
            return None
    
    def create_trimmed_video(self, video_path: str, start_frame: int, output_path: str) -> bool:
        """Create trimmed video starting from specified frame"""
        try:
            # Get video info
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()
            
            if start_frame >= total_frames:
                logger.error(f"Start frame {start_frame} >= total frames {total_frames}")
                return False
            
            # Calculate start time in seconds
            start_time = (start_frame - 1) / fps  # Convert to 0-based
            
            # Use ffmpeg to create trimmed video
            cmd = [
                'ffmpeg', '-y',  # -y to overwrite output file
                '-i', video_path,
                '-ss', str(start_time),
                '-c', 'copy',  # Copy streams without re-encoding for speed
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ Created trimmed video: {output_path}")
                return True
            else:
                logger.error(f"❌ ffmpeg failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to create trimmed video: {e}")
            return False

    def run_samurai_tracking(self,
                           video_path: str,
                           seed_frame: int,
                           bbox: List[float],
                           output_dir: Path) -> Dict[str, Any]:
        """Run SAMURAI tracking from seed frame onwards"""

        if self.samurai_predictor is None:
            logger.error("SAMURAI predictor not initialized")
            return {'status': 'failed', 'error': 'predictor_not_initialized'}

        try:
            logger.info(f"🎯 Running SAMURAI tracking on {video_path}")
            logger.info(f"📍 Seed frame: {seed_frame}, bbox: {bbox}")

            # Create trimmed video starting from seed frame
            trimmed_video_path = output_dir / "trimmed_video_from_seed.mp4"
            if not self.create_trimmed_video(video_path, seed_frame, str(trimmed_video_path)):
                return {'status': 'failed', 'error': 'failed_to_create_trimmed_video'}

            # Initialize SAMURAI state with trimmed video
            inference_state = self.samurai_predictor.init_state(video_path=str(trimmed_video_path))

            # Add bounding box as initial prompt at frame 0 of trimmed video
            samurai_seed_frame = 0  # First frame of trimmed video = seed frame of original
            self.samurai_predictor.add_new_points_or_box(
                inference_state=inference_state,
                frame_idx=samurai_seed_frame,
                obj_id=0,
                box=bbox
            )

            # Get video info
            cap = cv2.VideoCapture(str(trimmed_video_path))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()

            logger.info(f"📊 Trimmed video: {total_frames} frames, {fps} FPS")

            # Prepare output video
            output_video_path = output_dir / "samurai_tracking_result.mp4"
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_video_path), fourcc, fps, (width, height))

            # Track through video
            tracking_data = []
            last_valid_frame = 0
            consecutive_empty_frames = 0
            max_empty_frames = int(fps * 2)  # 2 seconds of empty frames before considering lost

            with torch.inference_mode(), torch.autocast(self.device, dtype=torch.float16):
                for samurai_frame_idx, (_, _, masks) in enumerate(
                    self.samurai_predictor.propagate_in_video(inference_state)
                ):
                    # Calculate actual video frame number
                    actual_video_frame = seed_frame + samurai_frame_idx

                    # Read frame from trimmed video
                    cap = cv2.VideoCapture(str(trimmed_video_path))
                    cap.set(cv2.CAP_PROP_POS_FRAMES, samurai_frame_idx)
                    ret, frame = cap.read()
                    cap.release()

                    if not ret:
                        break

                    frame_data = {
                        'frame_number': actual_video_frame,
                        'samurai_frame_idx': samurai_frame_idx,
                        'objects': []
                    }

                    # Process masks
                    has_valid_detection = False
                    if masks is not None and len(masks) > 0:
                        mask = masks[0]  # First object
                        if mask is not None:
                            mask_np = mask.cpu().numpy() > 0.5

                            # Check if mask has significant area
                            mask_area = np.sum(mask_np)
                            if mask_area > 100:  # Minimum area threshold
                                # Find bounding box from mask
                                coords = np.where(mask_np > 0.5)
                                if len(coords[0]) > 0:
                                    y1, y2 = coords[0].min(), coords[0].max()
                                    x1, x2 = coords[1].min(), coords[1].max()

                                    frame_data['objects'].append({
                                        'object_id': 0,
                                        'bbox': [float(x1), float(y1), float(x2), float(y2)],
                                        'mask_area': int(mask_area),
                                        'confidence': 1.0
                                    })

                                    # Draw bounding box and mask
                                    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                                    cv2.putText(frame, f'Frame:{actual_video_frame}', (int(x1), int(y1)-10),
                                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                                    # Overlay mask
                                    mask_colored = np.zeros_like(frame)
                                    mask_colored[:, :, 1] = (mask_np * 255).astype(np.uint8)
                                    frame = cv2.addWeighted(frame, 0.8, mask_colored, 0.2, 0)

                                    has_valid_detection = True
                                    last_valid_frame = samurai_frame_idx
                                    consecutive_empty_frames = 0

                    if not has_valid_detection:
                        consecutive_empty_frames += 1
                        # Add text indicating lost tracking
                        cv2.putText(frame, f'TRACKING LOST - Frame:{actual_video_frame}',
                                  (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

                    tracking_data.append(frame_data)
                    out.write(frame)

                    # Check if tracking is lost for too long
                    if consecutive_empty_frames > max_empty_frames:
                        logger.warning(f"⚠️ Tracking lost for {consecutive_empty_frames} frames, stopping at frame {actual_video_frame}")
                        break

            out.release()

            # Clean up trimmed video
            if trimmed_video_path.exists():
                trimmed_video_path.unlink()

            # Save tracking data
            tracking_json_path = output_dir / "tracking_data.json"
            with open(tracking_json_path, 'w') as f:
                json.dump({
                    'video_path': video_path,
                    'seed_frame': seed_frame,
                    'total_frames_processed': len(tracking_data),
                    'last_valid_frame': seed_frame + last_valid_frame,
                    'tracking_lost': consecutive_empty_frames > max_empty_frames,
                    'tracking_results': tracking_data
                }, f, indent=2)

            logger.info(f"✅ SAMURAI tracking completed")
            logger.info(f"📹 Output video: {output_video_path}")
            logger.info(f"📊 Tracking data: {tracking_json_path}")

            return {
                'status': 'success',
                'output_video': str(output_video_path),
                'tracking_data': str(tracking_json_path),
                'frames_processed': len(tracking_data),
                'last_valid_frame': seed_frame + last_valid_frame,
                'tracking_lost': consecutive_empty_frames > max_empty_frames
            }

        except Exception as e:
            logger.error(f"❌ SAMURAI tracking failed: {e}")
            import traceback
            traceback.print_exc()
            return {'status': 'failed', 'error': str(e)}

    def re_detect_product(self, video_path: str, start_frame: int) -> Optional[Dict[str, Any]]:
        """Re-detect product using auto_labelme_yolo from specified frame onwards"""
        try:
            logger.info(f"🔍 Re-detecting product in {video_path} from frame {start_frame}")

            # Run auto_labelme_yolo with specific frame
            cmd = ['python', 'auto_labelme_yolo.py', video_path, str(start_frame)]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                logger.error(f"❌ Auto labelme failed: {result.stderr}")
                return None

            # Parse output to find the generated directory
            output_lines = result.stdout.split('\n')
            detection_info = None

            for line in output_lines:
                if 'Final detection:' in line:
                    # Extract detection info
                    parts = line.split()
                    if len(parts) >= 4:
                        label = parts[2]
                        score = parts[6].rstrip(')')
                        detection_info = {'label': label, 'score': float(score)}
                elif 'Detection:' in line and 'coordinates' in line:
                    # Extract coordinates
                    coords_start = line.find('(')
                    coords_end = line.find(')')
                    if coords_start != -1 and coords_end != -1:
                        coords_str = line[coords_start+1:coords_end]
                        coords = [float(x.strip()) for x in coords_str.split(',')]
                        if detection_info:
                            detection_info['bbox'] = coords
                elif 'Saved LabelMe JSON' in line:
                    # Extract output directory
                    json_path = line.split(': ')[1]
                    output_dir = os.path.dirname(json_path)
                    if detection_info:
                        detection_info['output_dir'] = output_dir

            if detection_info and 'bbox' in detection_info and 'output_dir' in detection_info:
                logger.info(f"✅ Re-detected {detection_info['label']} at frame {start_frame}")
                return detection_info
            else:
                logger.warning(f"⚠️ No valid detection found from frame {start_frame}")
                return None

        except Exception as e:
            logger.error(f"❌ Re-detection failed: {e}")
            return None

    def process_auto_labelme_output(self, output_dir: str) -> Dict[str, Any]:
        """Main pipeline: process auto_labelme_yolo output with SAMURAI tracking"""

        # Load auto_labelme results
        labelme_data = self.load_auto_labelme_results(output_dir)
        if not labelme_data:
            return {'status': 'failed', 'error': 'failed_to_load_labelme_data'}

        # Create output directory for SAMURAI results
        samurai_output_dir = Path(output_dir) / "samurai_tracking"
        samurai_output_dir.mkdir(exist_ok=True)

        results = {
            'status': 'success',
            'video_path': labelme_data['video_path'],
            'video_name': labelme_data['video_name'],
            'initial_detection': labelme_data,
            'tracking_segments': []
        }

        current_frame = labelme_data['seed_frame']
        current_bbox = labelme_data['bbox']
        segment_count = 0

        while True:
            segment_count += 1
            logger.info(f"\n🚀 Starting tracking segment {segment_count} from frame {current_frame}")

            # Create segment output directory
            segment_dir = samurai_output_dir / f"segment_{segment_count:02d}"
            segment_dir.mkdir(exist_ok=True)

            # Run SAMURAI tracking
            tracking_result = self.run_samurai_tracking(
                video_path=labelme_data['video_path'],
                seed_frame=current_frame,
                bbox=current_bbox,
                output_dir=segment_dir
            )

            segment_info = {
                'segment_id': segment_count,
                'start_frame': current_frame,
                'bbox': current_bbox,
                'tracking_result': tracking_result,
                'output_dir': str(segment_dir)
            }

            if tracking_result['status'] != 'success':
                logger.error(f"❌ Tracking segment {segment_count} failed")
                segment_info['status'] = 'failed'
                results['tracking_segments'].append(segment_info)
                break

            # Check if tracking was lost
            if not tracking_result.get('tracking_lost', False):
                logger.info(f"✅ Tracking segment {segment_count} completed successfully")
                segment_info['status'] = 'completed'
                results['tracking_segments'].append(segment_info)
                break

            # Tracking was lost, try to re-detect
            last_valid_frame = tracking_result['last_valid_frame']
            logger.info(f"⚠️ Tracking lost at frame {last_valid_frame}, attempting re-detection")

            # Try re-detection from a few frames after the last valid frame
            re_detect_start = last_valid_frame + int(30)  # Skip ~1 second ahead

            re_detection = self.re_detect_product(labelme_data['video_path'], re_detect_start)

            if re_detection is None:
                logger.warning(f"❌ Re-detection failed, ending tracking")
                segment_info['status'] = 'tracking_lost_no_redetection'
                results['tracking_segments'].append(segment_info)
                break

            # Continue with re-detected product
            segment_info['status'] = 'tracking_lost_redetected'
            segment_info['re_detection'] = re_detection
            results['tracking_segments'].append(segment_info)

            # Update for next segment
            current_frame = re_detect_start
            current_bbox = re_detection['bbox']

            logger.info(f"🔄 Continuing tracking from frame {current_frame} with re-detected product")

        # Save overall results
        results_json_path = samurai_output_dir / "pipeline_results.json"
        with open(results_json_path, 'w') as f:
            json.dump(results, f, indent=2)

        logger.info(f"\n🎉 Pipeline completed with {len(results['tracking_segments'])} segments")
        logger.info(f"📄 Results saved to: {results_json_path}")

        return results


def main():
    """Main function"""
    if len(sys.argv) < 2 or sys.argv[1] in ['-h', '--help']:
        print("Usage: python auto_samurai_pipeline.py <auto_labelme_output_dir>")
        print("")
        print("Examples:")
        print("  python auto_samurai_pipeline.py output_held_detection_amino_energy_grape_frame60")
        print("  python auto_samurai_pipeline.py output_held_detection_pepsi_zero_sugar_frame125")
        print("")
        print("This script:")
        print("  1. Loads detection results from auto_labelme_yolo")
        print("  2. Runs SAMURAI tracking from the detection frame onwards")
        print("  3. Detects when tracking fails (product moves out of frame)")
        print("  4. Automatically re-detects and continues tracking")
        print("  5. Saves results in organized subdirectories")
        sys.exit(0)

    output_dir = sys.argv[1]

    if not os.path.exists(output_dir):
        logger.error(f"Output directory not found: {output_dir}")
        sys.exit(1)

    # Create pipeline
    pipeline = AutoSamuraiPipeline()

    # Initialize SAMURAI
    logger.info("🤖 Initializing SAMURAI model...")
    if not pipeline.initialize_samurai():
        logger.error("❌ Failed to initialize SAMURAI model")
        sys.exit(1)

    # Process the auto_labelme output
    logger.info(f"🚀 Processing auto_labelme output: {output_dir}")
    results = pipeline.process_auto_labelme_output(output_dir)

    if results['status'] == 'success':
        logger.info("🎉 Pipeline completed successfully!")

        # Print summary
        segments = results['tracking_segments']
        logger.info(f"\n📊 SUMMARY:")
        logger.info(f"Video: {results['video_name']}")
        logger.info(f"Total segments: {len(segments)}")

        for i, segment in enumerate(segments, 1):
            status = segment['status']
            start_frame = segment['start_frame']
            logger.info(f"  Segment {i}: {status} (started at frame {start_frame})")

            if segment['tracking_result']['status'] == 'success':
                frames_processed = segment['tracking_result']['frames_processed']
                logger.info(f"    Processed {frames_processed} frames")
    else:
        logger.error(f"❌ Pipeline failed: {results.get('error', 'unknown error')}")
        sys.exit(1)


if __name__ == "__main__":
    main()
